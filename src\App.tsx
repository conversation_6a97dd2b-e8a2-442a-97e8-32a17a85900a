import { Toaster } from "@/components/ui/toaster";
import { Toaster as Son<PERSON> } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import DashboardLayout from "./components/DashboardLayout";
import Dashboard from "./pages/Dashboard";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import AuthPage from "./components/AuthPage";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/auth" element={<AuthPage />} />
          <Route path="/text-editor" element={<Index />} />
          <Route path="/" element={<DashboardLayout />}>
            <Route index element={<Navigate to="/dashboard" replace />} />
            <Route path="dashboard" element={<Dashboard />} />
            <Route path="chat" element={<div className="p-8 text-center">Chat feature coming soon!</div>} />
            <Route path="image-generation" element={<div className="p-8 text-center">Image Generation coming soon!</div>} />
            <Route path="image-upscaler" element={<div className="p-8 text-center">Image Upscaler coming soon!</div>} />
            <Route path="video-generation" element={<div className="p-8 text-center">Video Generation coming soon!</div>} />
            <Route path="image-to-video" element={<div className="p-8 text-center">Image To Video coming soon!</div>} />
            <Route path="speech-generator" element={<div className="p-8 text-center">Speech Generator coming soon!</div>} />
            <Route path="speech-to-text" element={<div className="p-8 text-center">Speech To Text coming soon!</div>} />
            <Route path="talking-ai-avatar" element={<div className="p-8 text-center">Talking AI Avatar coming soon!</div>} />
          </Route>
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
