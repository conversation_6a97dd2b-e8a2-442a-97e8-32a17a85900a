import { 
  Home, 
  MessageSquare, 
  Image, 
  ArrowUpRight, 
  Video, 
  FileImage, 
  Volume2, 
  Mic,
  Bot
} from "lucide-react";
import { NavLink, useLocation } from "react-router-dom";

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";

const menuItems = [
  { title: "Dashboard", url: "/dashboard", icon: Home },
  { title: "Text Editor", url: "/text-editor", icon: FileImage },
  { title: "Chat", url: "/chat", icon: MessageSquare },
  { title: "Image Generation", url: "/image-generation", icon: Image },
  { title: "Image Upscaler", url: "/image-upscaler", icon: ArrowUpRight },
  { title: "Video Generation", url: "/video-generation", icon: Video },
  { title: "Image To Video", url: "/image-to-video", icon: FileImage },
  { title: "Speech Generator", url: "/speech-generator", icon: Volume2 },
  { title: "Speech To Text", url: "/speech-to-text", icon: Mic },
  { title: "Talking AI Avatar", url: "/talking-ai-avatar", icon: Bot },
];

export function AppSidebar() {
  const { state } = useSidebar();
  const location = useLocation();
  const currentPath = location.pathname;
  const collapsed = state === "collapsed";

  const isActive = (path: string) => currentPath === path;
  const getNavCls = ({ isActive }: { isActive: boolean }) =>
    isActive ? "bg-sidebar-accent text-sidebar-primary font-medium" : "hover:bg-sidebar-accent/50";

  return (
    <Sidebar className={collapsed ? "w-14" : "w-64"} collapsible="icon">
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel className="text-sidebar-foreground/70 text-xs font-semibold uppercase tracking-wider px-2">
            AI Tools
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {menuItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild>
                    <NavLink to={item.url} className={getNavCls}>
                      <item.icon className="h-4 w-4" />
                      {!collapsed && <span>{item.title}</span>}
                    </NavLink>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}