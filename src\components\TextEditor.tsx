import { useState, useEffect, useCallback } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Type, Bold, Italic, Underline, Palette } from "lucide-react";
import { Card } from "@/components/ui/card";

interface TextEditorProps {
  onAddText: (textOptions: TextOptions) => void;
  onUpdateText?: (textOptions: TextOptions) => void;
  selectedTextOptions?: TextOptions | null;
}

export interface TextOptions {
  text: string;
  fontFamily: string;
  fontSize: number;
  fontWeight: string;
  fontStyle: string;
  textDecoration: string;
  fill: string;
  stroke?: string;
  strokeWidth?: number;
  shadow?: {
    blur: number;
    offsetX: number;
    offsetY: number;
    color: string;
  };
}

export const TextEditor = ({ onAddText, onUpdateText, selectedTextOptions }: TextEditorProps) => {
  const [text, setText] = useState("Add your text");
  const [fontFamily, setFontFamily] = useState("Arial");
  const [fontSize, setFontSize] = useState([32]);
  const [fontWeight, setFontWeight] = useState("normal");
  const [fontStyle, setFontStyle] = useState("normal");
  const [textDecoration, setTextDecoration] = useState("normal");
  const [fill, setFill] = useState("#000000"); // Changed to black for visibility
  const [stroke, setStroke] = useState("#000000");
  const [strokeWidth, setStrokeWidth] = useState([0]);
  const [shadowBlur, setShadowBlur] = useState([0]);
  const [shadowOffsetX, setShadowOffsetX] = useState([2]);
  const [shadowOffsetY, setShadowOffsetY] = useState([2]);
  const [shadowColor, setShadowColor] = useState("#000000");

  const fonts = [
    "Arial", "Helvetica", "Times New Roman", "Georgia", "Verdana",
    "Comic Sans MS", "Impact", "Trebuchet MS", "Courier New", "Palatino"
  ];

  const presetColors = [
    "#ffffff", "#000000", "#ff0000", "#00ff00", "#0000ff",
    "#ffff00", "#ff00ff", "#00ffff", "#ffa500", "#800080"
  ];

  // Populate form fields when a text object is selected
  useEffect(() => {
    if (selectedTextOptions) {
      setText(selectedTextOptions.text);
      setFontFamily(selectedTextOptions.fontFamily);
      setFontSize([selectedTextOptions.fontSize]);
      setFontWeight(selectedTextOptions.fontWeight);
      setFontStyle(selectedTextOptions.fontStyle);
      setTextDecoration(selectedTextOptions.textDecoration);
      setFill(selectedTextOptions.fill);
      if (selectedTextOptions.stroke) {
        setStroke(selectedTextOptions.stroke);
      }
      if (selectedTextOptions.strokeWidth !== undefined) {
        setStrokeWidth([selectedTextOptions.strokeWidth]);
      }
      if (selectedTextOptions.shadow) {
        setShadowBlur([selectedTextOptions.shadow.blur]);
        setShadowOffsetX([selectedTextOptions.shadow.offsetX]);
        setShadowOffsetY([selectedTextOptions.shadow.offsetY]);
        setShadowColor(selectedTextOptions.shadow.color);
      } else {
        setShadowBlur([0]);
        setShadowOffsetX([2]);
        setShadowOffsetY([2]);
        setShadowColor("#000000");
      }
    }
  }, [selectedTextOptions]);

  const createTextOptions = (): TextOptions => {
    return {
      text,
      fontFamily,
      fontSize: fontSize[0],
      fontWeight,
      fontStyle,
      textDecoration,
      fill,
      stroke: strokeWidth[0] > 0 ? stroke : undefined,
      strokeWidth: strokeWidth[0] > 0 ? strokeWidth[0] : undefined,
      shadow: shadowBlur[0] > 0 ? {
        blur: shadowBlur[0],
        offsetX: shadowOffsetX[0],
        offsetY: shadowOffsetY[0],
        color: shadowColor
      } : undefined
    };
  };

  // Apply real-time updates when properties change
  const applyRealTimeUpdate = useCallback(() => {
    if (selectedTextOptions && onUpdateText) {
      const textOptions: TextOptions = {
        text,
        fontFamily,
        fontSize: fontSize[0],
        fontWeight,
        fontStyle,
        textDecoration,
        fill,
        stroke: strokeWidth[0] > 0 ? stroke : undefined,
        strokeWidth: strokeWidth[0] > 0 ? strokeWidth[0] : undefined,
        shadow: shadowBlur[0] > 0 ? {
          blur: shadowBlur[0],
          offsetX: shadowOffsetX[0],
          offsetY: shadowOffsetY[0],
          color: shadowColor
        } : undefined
      };
      onUpdateText(textOptions);
    }
  }, [selectedTextOptions, onUpdateText, text, fontFamily, fontSize, fontWeight, fontStyle, textDecoration, fill, stroke, strokeWidth, shadowBlur, shadowOffsetX, shadowOffsetY, shadowColor]);

  // Trigger real-time updates when properties change (but only if text is selected)
  useEffect(() => {
    if (selectedTextOptions) {
      applyRealTimeUpdate();
    }
  }, [text, fontFamily, fontSize, fontWeight, fontStyle, textDecoration, fill, stroke, strokeWidth, shadowBlur, shadowOffsetX, shadowOffsetY, shadowColor, applyRealTimeUpdate, selectedTextOptions]);

  const handleAddText = () => {
    const textOptions = createTextOptions();
    onAddText(textOptions);
  };

  return (
    <Card className="p-6 space-y-6 bg-card border-border">
      <div className="flex items-center gap-2 mb-4">
        <Type className="h-5 w-5 text-primary" />
        <h3 className="text-lg font-semibold">Text Properties</h3>
      </div>

      {/* Text Input */}
      <div className="space-y-2">
        <Label htmlFor="text-input">Text</Label>
        <Input
          id="text-input"
          value={text}
          onChange={(e) => setText(e.target.value)}
          placeholder="Enter your text"
          className="bg-background"
        />
      </div>

      {/* Font Family */}
      <div className="space-y-2">
        <Label>Font Family</Label>
        <Select value={fontFamily} onValueChange={setFontFamily}>
          <SelectTrigger className="bg-background">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {fonts.map((font) => (
              <SelectItem key={font} value={font} style={{ fontFamily: font }}>
                {font}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Font Size */}
      <div className="space-y-2">
        <Label>Font Size: {fontSize[0]}px</Label>
        <Slider
          value={fontSize}
          onValueChange={setFontSize}
          min={12}
          max={120}
          step={1}
          className="w-full"
        />
      </div>

      {/* Font Style Controls */}
      <div className="space-y-2">
        <Label>Style</Label>
        <div className="flex gap-2">
          <Button
            variant={fontWeight === "bold" ? "tool-active" : "tool"}
            size="tool"
            onClick={() => setFontWeight(fontWeight === "bold" ? "normal" : "bold")}
          >
            <Bold className="h-4 w-4" />
          </Button>
          <Button
            variant={fontStyle === "italic" ? "tool-active" : "tool"}
            size="tool"
            onClick={() => setFontStyle(fontStyle === "italic" ? "normal" : "italic")}
          >
            <Italic className="h-4 w-4" />
          </Button>
          <Button
            variant={textDecoration === "underline" ? "tool-active" : "tool"}
            size="tool"
            onClick={() => setTextDecoration(textDecoration === "underline" ? "normal" : "underline")}
          >
            <Underline className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Colors */}
      <div className="space-y-4">
        <div className="space-y-2">
          <Label>Text Color</Label>
          <div className="flex items-center gap-2">
            <input
              type="color"
              value={fill}
              onChange={(e) => setFill(e.target.value)}
              className="w-8 h-8 rounded border border-border cursor-pointer"
            />
            <div className="flex gap-1">
              {presetColors.map((color) => (
                <button
                  key={color}
                  onClick={() => setFill(color)}
                  className="w-6 h-6 rounded border border-border cursor-pointer hover:scale-110 transition-transform"
                  style={{ backgroundColor: color }}
                />
              ))}
            </div>
          </div>
        </div>

        {/* Stroke */}
        <div className="space-y-2">
          <Label>Stroke Width: {strokeWidth[0]}px</Label>
          <Slider
            value={strokeWidth}
            onValueChange={setStrokeWidth}
            min={0}
            max={10}
            step={1}
            className="w-full"
          />
          {strokeWidth[0] > 0 && (
            <div className="flex items-center gap-2">
              <Label className="text-sm">Stroke Color:</Label>
              <input
                type="color"
                value={stroke}
                onChange={(e) => setStroke(e.target.value)}
                className="w-6 h-6 rounded border border-border cursor-pointer"
              />
            </div>
          )}
        </div>
      </div>

      {/* Shadow Effects */}
      <div className="space-y-4">
        <div className="space-y-2">
          <Label>Shadow Blur: {shadowBlur[0]}px</Label>
          <Slider
            value={shadowBlur}
            onValueChange={setShadowBlur}
            min={0}
            max={20}
            step={1}
            className="w-full"
          />
        </div>

        {shadowBlur[0] > 0 && (
          <>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Shadow X: {shadowOffsetX[0]}px</Label>
                <Slider
                  value={shadowOffsetX}
                  onValueChange={setShadowOffsetX}
                  min={-10}
                  max={10}
                  step={1}
                  className="w-full"
                />
              </div>
              <div className="space-y-2">
                <Label>Shadow Y: {shadowOffsetY[0]}px</Label>
                <Slider
                  value={shadowOffsetY}
                  onValueChange={setShadowOffsetY}
                  min={-10}
                  max={10}
                  step={1}
                  className="w-full"
                />
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Label className="text-sm">Shadow Color:</Label>
              <input
                type="color"
                value={shadowColor}
                onChange={(e) => setShadowColor(e.target.value)}
                className="w-6 h-6 rounded border border-border cursor-pointer"
              />
            </div>
          </>
        )}
      </div>

      <div className="space-y-3">
        {selectedTextOptions && (
          <div className="p-3 bg-primary/10 border border-primary/20 rounded-lg">
            <div className="flex items-center gap-2 text-sm text-primary">
              <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
              <span className="font-medium">Editing Selected Text</span>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Changes are applied in real-time
            </p>
          </div>
        )}

        <Button
          onClick={handleAddText}
          className="w-full"
          variant={selectedTextOptions ? "outline" : "gradient"}
          size="lg"
        >
          <Palette className="h-4 w-4 mr-2" />
          {selectedTextOptions ? "Add New Text" : "Add Text to Canvas"}
        </Button>
      </div>
    </Card>
  );
};