// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://dlezfbmgdupvfotwmgor.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRsZXpmYm1nZHVwdmZvdHdtZ29yIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQwOTE0ODksImV4cCI6MjA2OTY2NzQ4OX0.trbCVdBIPmGska-QpdMJXOc1iVqC8WKohBKf8LSrkP4";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});