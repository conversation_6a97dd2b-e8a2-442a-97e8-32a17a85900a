import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { 
  MessageSquare, 
  Image, 
  ArrowUpRight, 
  Video, 
  FileImage, 
  Volume2, 
  Mic,
  Bot,
  ArrowRight
} from "lucide-react";
import { useNavigate } from "react-router-dom";

const tools = [
  {
    title: "Text Editor",
    description: "Add text overlays to your images with ease.",
    icon: FileImage,
    route: "/text-editor",
    color: "text-blue-600"
  },
  {
    title: "AI Chat",
    description: "Intelligent conversations with context-aware responses.",
    icon: MessageSquare,
    route: "/chat",
    color: "text-indigo-600"
  },
  {
    title: "Image Generation",
    description: "Create stunning images from text prompts.",
    icon: Image,
    route: "/image-generation",
    color: "text-purple-600"
  },
  {
    title: "Image Upscaler",
    description: "Enhance low-res images without quality loss.",
    icon: ArrowUpRight,
    route: "/image-upscaler",
    color: "text-green-600"
  },
  {
    title: "Video Generation",
    description: "Generate videos from text or scripts.",
    icon: Video,
    route: "/video-generation",
    color: "text-red-600"
  },
  {
    title: "Image To Video",
    description: "Transform static images into dynamic videos.",
    icon: FileImage,
    route: "/image-to-video",
    color: "text-orange-600"
  },
  {
    title: "Speech Generator",
    description: "Convert text to natural speech.",
    icon: Volume2,
    route: "/speech-generator",
    color: "text-pink-600"
  },
  {
    title: "Speech To Text",
    description: "Transcribe audio to accurate text.",
    icon: Mic,
    route: "/speech-to-text",
    color: "text-indigo-600"
  },
  {
    title: "Talking AI Avatar",
    description: "Create realistic talking avatars.",
    icon: Bot,
    route: "/talking-ai-avatar",
    color: "text-teal-600"
  }
];

export default function Dashboard() {
  const navigate = useNavigate();

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-foreground">AI Super App Dashboard</h1>
        <p className="text-muted-foreground">All your AI tools in one powerful platform</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {tools.map((tool) => (
          <Card key={tool.title} className="group hover:shadow-lg transition-all duration-200 hover:scale-[1.02]">
            <CardHeader className="pb-3">
              <div className={`w-12 h-12 rounded-lg bg-gradient-to-br from-primary/10 to-accent/10 flex items-center justify-center mb-3`}>
                <tool.icon className={`h-6 w-6 ${tool.color}`} />
              </div>
              <CardTitle className="text-lg">{tool.title}</CardTitle>
              <CardDescription className="text-sm">{tool.description}</CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              <Button 
                onClick={() => navigate(tool.route)}
                className="w-full group-hover:translate-x-1 transition-transform duration-200"
                variant="outline"
              >
                Try it out
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}