import { useState, useCallback, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { ImageUpload } from "@/components/ImageUpload";
import { TextEditor, TextOptions } from "@/components/TextEditor";
import { CanvasEditor } from "@/components/CanvasEditor";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/hooks/useAuth";

const Index = () => {
  const [imageUrl, setImageUrl] = useState<string>("");
  const [addTextToCanvas, setAddTextToCanvas] = useState<((textOptions: TextOptions) => void) | null>(null);
  const [updateTextOnCanvas, setUpdateTextOnCanvas] = useState<((textOptions: TextOptions) => void) | null>(null);
  const [selectedTextOptions, setSelectedTextOptions] = useState<TextOptions | null>(null);
  const { user, loading, signOut } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (!loading && !user) {
      navigate('/auth');
    }
  }, [user, loading, navigate]);

  const handleSetAddTextToCanvas = useCallback((fn: (textOptions: TextOptions) => void) => {
    setAddTextToCanvas(() => fn);
  }, []);

  const handleSetUpdateTextOnCanvas = useCallback((fn: (textOptions: TextOptions) => void) => {
    setUpdateTextOnCanvas(() => fn);
  }, []);

  const handleTextSelected = useCallback((textOptions: TextOptions | null) => {
    setSelectedTextOptions(textOptions);
  }, []);

  const handleImageUpload = useCallback((url: string) => {
    setImageUrl(url);
  }, []);

  const handleAddText = useCallback((textOptions: TextOptions) => {
    if (addTextToCanvas && textOptions) {
      addTextToCanvas(textOptions);
    }
  }, [addTextToCanvas]);

  const handleUpdateText = useCallback((textOptions: TextOptions) => {
    if (updateTextOnCanvas && textOptions) {
      updateTextOnCanvas(textOptions);
    }
  }, [updateTextOnCanvas]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-muted-foreground">Loading...</div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border bg-card/50 backdrop-blur-sm sticky top-0 z-10">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold bg-gradient-primary bg-clip-text text-transparent">
                Text Overlay Editor
              </h1>
              <p className="text-muted-foreground text-sm">
                Add beautiful text overlays to your images
              </p>
            </div>
            <Button 
              variant="outline" 
              onClick={signOut}
              className="text-muted-foreground hover:text-foreground"
            >
              Sign Out
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Sidebar - Tools */}
          <div className="lg:col-span-1 space-y-6">
            {!imageUrl && (
              <div className="animate-fade-in">
                <ImageUpload onImageUpload={handleImageUpload} />
              </div>
            )}
            
            {imageUrl && (
              <div className="animate-slide-in">
                <TextEditor
                  onAddText={handleAddText}
                  onUpdateText={handleUpdateText}
                  selectedTextOptions={selectedTextOptions}
                />
              </div>
            )}
          </div>

          {/* Main Canvas Area */}
          <div className="lg:col-span-2">
            <div className="animate-fade-in">
              <CanvasEditor
                imageUrl={imageUrl}
                onAddText={handleSetAddTextToCanvas}
                onUpdateText={handleSetUpdateTextOnCanvas}
                onTextSelected={handleTextSelected}
              />
            </div>
          </div>
        </div>

        {/* Instructions */}
        {!imageUrl && (
          <div className="mt-12 text-center space-y-4 animate-fade-in">
            <h2 className="text-xl font-semibold text-foreground">
              How to use Text Overlay Editor
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
              <div className="space-y-2">
                <div className="w-12 h-12 bg-gradient-primary rounded-full flex items-center justify-center mx-auto text-primary-foreground font-bold">
                  1
                </div>
                <h3 className="font-semibold">Upload Image</h3>
                <p className="text-muted-foreground text-sm">
                  Drag and drop or click to upload your image
                </p>
              </div>
              <div className="space-y-2">
                <div className="w-12 h-12 bg-gradient-primary rounded-full flex items-center justify-center mx-auto text-primary-foreground font-bold">
                  2
                </div>
                <h3 className="font-semibold">Customize Text</h3>
                <p className="text-muted-foreground text-sm">
                  Add text with custom fonts, colors, and effects
                </p>
              </div>
              <div className="space-y-2">
                <div className="w-12 h-12 bg-gradient-primary rounded-full flex items-center justify-center mx-auto text-primary-foreground font-bold">
                  3
                </div>
                <h3 className="font-semibold">Export</h3>
                <p className="text-muted-foreground text-sm">
                  Download your image with text overlays
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Index;